/**
 * PuppeteerAdapter
 * Phase 01: PBW Foundation & Tool Setup
 * Direct Puppeteer integration (no MCP needed)
 */

import { EventEmitter } from 'events';
import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';

export interface PuppeteerConfig {
  headless?: boolean;
  defaultTimeout?: number;
  viewportWidth?: number;
  viewportHeight?: number;
}

export class PuppeteerAdapter extends EventEmitter {
  private browser?: Browser;
  private config: PuppeteerConfig;

  constructor(config?: PuppeteerConfig) {
    super();
    this.config = {
      headless: true,
      defaultTimeout: 30000,
      viewportWidth: 1920,
      viewportHeight: 1080,
      ...config
    };
  }

  /**
   * Initialize browser
   */
  public async initialize(): Promise<void> {
    try {
      this.browser = await puppeteer.launch({
        headless: this.config.headless,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      this.emit('initialized');
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Execute browser test
   */
  public async executeTest(script: string): Promise<any> {
    if (!this.browser) {
      await this.initialize();
    }

    const page = await this.browser!.newPage();
    
    try {
      await page.setViewport({
        width: this.config.viewportWidth!,
        height: this.config.viewportHeight!
      });

      // Execute the test script
      const result = await page.evaluate(script);
      
      return {
        success: true,
        result,
        metrics: await page.metrics()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    } finally {
      await page.close();
    }
  }

  /**
   * Test a URL
   */
  public async testURL(url: string, tests: Array<{
    selector: string;
    action?: 'click' | 'type' | 'exists';
    value?: string;
  }>): Promise<any> {
    if (!this.browser) {
      await this.initialize();
    }

    const page = await this.browser!.newPage();
    
    try {
      await page.goto(url, { waitUntil: 'networkidle2' });
      
      const results = [];
      
      for (const test of tests) {
        try {
          switch (test.action) {
            case 'click':
              await page.click(test.selector);
              results.push({ selector: test.selector, action: 'click', success: true });
              break;
              
            case 'type':
              await page.type(test.selector, test.value || '');
              results.push({ selector: test.selector, action: 'type', success: true });
              break;
              
            case 'exists':
            default:
              const exists = await page.$(test.selector) !== null;
              results.push({ selector: test.selector, exists, success: true });
              break;
          }
        } catch (error) {
          results.push({ 
            selector: test.selector, 
            action: test.action, 
            success: false, 
            error: error.message 
          });
        }
      }
      
      return {
        url,
        success: true,
        results,
        screenshot: await page.screenshot({ encoding: 'base64' })
      };
      
    } catch (error) {
      return {
        url,
        success: false,
        error: error.message
      };
    } finally {
      await page.close();
    }
  }

  /**
   * Capture screenshot
   */
  public async screenshot(url: string, fullPage: boolean = false): Promise<Buffer> {
    if (!this.browser) {
      await this.initialize();
    }

    const page = await this.browser!.newPage();
    
    try {
      await page.goto(url, { waitUntil: 'networkidle2' });
      const screenshot = await page.screenshot({ fullPage });
      return Buffer.from(screenshot);
    } finally {
      await page.close();
    }
  }

  /**
   * Close browser
   */
  public async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = undefined;
    }
  }
} 