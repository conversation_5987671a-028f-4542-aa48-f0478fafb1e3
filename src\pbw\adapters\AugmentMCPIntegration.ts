/**
 * AugmentMCPIntegration
 * Phase 01: PBW Foundation & Tool Setup
 * Integrates with Augment's built-in MCP server
 */

import { EventEmitter } from 'events';
import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import * as net from 'net';

const execAsync = promisify(exec);

export class AugmentMCPIntegration extends EventEmitter {
  private connected: boolean = false;
  private client?: net.Socket;
  private messageQueue: Map<string, (response: any) => void> = new Map();

  /**
   * Check if Augment is running
   */
  public async checkAugmentStatus(): Promise<{
    running: boolean;
    pid?: number;
    mcpPort?: number;
  }> {
    try {
      // Check for Augment process on Windows
      const { stdout } = await execAsync('tasklist /FI "IMAGENAME eq Augment.exe" /FO CSV');
      const isRunning = stdout.includes('Augment.exe');
      
      if (!isRunning) {
        // Check for Mac/Linux
        const { stdout: psOut } = await execAsync('ps aux | grep -i augment | grep -v grep');
        if (psOut.trim()) {
          return { running: true };
        }
      }
      
      // Try to find MCP port from Augment config
      const mcpPort = await this.findAugmentMCPPort();
      
      return {
        running: isRunning || false,
        mcpPort
      };
    } catch (error) {
      return { running: false };
    }
  }

  /**
   * Find Augment's MCP port from config
   */
  private async findAugmentMCPPort(): Promise<number | undefined> {
    // Augment typically uses these locations for config
    const configPaths = [
      `${process.env.APPDATA}/Augment/config.json`,
      `${process.env.HOME}/.augment/config.json`,
      `${process.env.HOME}/Library/Application Support/Augment/config.json`
    ];

    for (const path of configPaths) {
      try {
        const fs = await import('fs/promises');
        const config = JSON.parse(await fs.readFile(path, 'utf-8'));
        if (config.mcp?.port) {
          return config.mcp.port;
        }
      } catch (error) {
        // Continue to next path
      }
    }

    // Default Augment MCP port
    return 50051;
  }

  /**
   * Connect to Augment's MCP server
   */
  public async connect(): Promise<void> {
    const status = await this.checkAugmentStatus();
    
    if (!status.running) {
      throw new Error('Augment is not running. Please start Augment first.');
    }

    const port = status.mcpPort || 50051;
    
    return new Promise((resolve, reject) => {
      this.client = new net.Socket();
      
      this.client.connect(port, 'localhost', () => {
        this.connected = true;
        this.emit('connected', { port });
        resolve();
      });

      this.client.on('data', (data) => {
        this.handleData(data);
      });

      this.client.on('error', (error: any) => {
        this.connected = false;
        if (error.code === 'ECONNREFUSED') {
          reject(new Error(`Cannot connect to Augment MCP on port ${port}. Make sure Augment is running with MCP enabled.`));
        } else {
          reject(error);
        }
      });

      this.client.on('close', () => {
        this.connected = false;
        this.emit('disconnected');
      });

      // Timeout after 5 seconds
      setTimeout(() => {
        if (!this.connected) {
          reject(new Error('Connection timeout. Augment MCP might not be enabled.'));
        }
      }, 5000);
    });
  }

  /**
   * Send MCP request to Augment
   */
  public async request(method: string, params?: any): Promise<any> {
    if (!this.connected || !this.client) {
      throw new Error('Not connected to Augment MCP');
    }

    const id = `${Date.now()}-${Math.random()}`;
    const message = {
      jsonrpc: '2.0',
      id,
      method,
      params: params || {}
    };

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.messageQueue.delete(id);
        reject(new Error(`Request timeout: ${method}`));
      }, 30000);

      this.messageQueue.set(id, (response) => {
        clearTimeout(timeout);
        this.messageQueue.delete(id);

        if (response.error) {
          reject(new Error(response.error.message || 'Unknown error'));
        } else {
          resolve(response.result);
        }
      });

      this.client!.write(JSON.stringify(message) + '\n');
    });
  }

  /**
   * Handle incoming data
   */
  private handleData(data: Buffer): void {
    const lines = data.toString().split('\n').filter(line => line.trim());
    
    lines.forEach(line => {
      try {
        const message = JSON.parse(line);
        
        if (message.id && this.messageQueue.has(message.id)) {
          const callback = this.messageQueue.get(message.id)!;
          callback(message);
        } else if (message.method) {
          // Server notification
          this.emit('notification', message);
        }
      } catch (error) {
        this.emit('parse:error', { error, data: line });
      }
    });
  }

  /**
   * Augment-specific MCP methods
   */

  /**
   * Get code suggestions
   */
  public async getSuggestions(context: {
    file: string;
    position: { line: number; column: number };
    code: string;
  }): Promise<any> {
    return this.request('textDocument/completion', {
      textDocument: { uri: `file://${context.file}` },
      position: context.position,
      context: { triggerKind: 1 }
    });
  }

  /**
   * Analyze code
   */
  public async analyzeCode(code: string, language: string = 'typescript'): Promise<{
    issues: Array<{
      severity: 'error' | 'warning' | 'info';
      message: string;
      line?: number;
      column?: number;
    }>;
    suggestions: string[];
  }> {
    return this.request('augment/analyze', {
      code,
      language,
      options: {
        includeStyleIssues: true,
        includeSecurity: true,
        includePerformance: true
      }
    });
  }

  /**
   * Generate tests
   */
  public async generateTests(code: string, framework: string = 'jest'): Promise<string> {
    return this.request('augment/generateTests', {
      code,
      framework,
      options: {
        coverage: 'high',
        includeEdgeCases: true
      }
    });
  }

  /**
   * Refactor code
   */
  public async refactorCode(code: string, refactorType: string): Promise<string> {
    return this.request('augment/refactor', {
      code,
      refactorType,
      options: {
        preserveComments: true,
        modernize: true
      }
    });
  }

  /**
   * Start Augment if not running
   */
  public async startAugment(): Promise<void> {
    const status = await this.checkAugmentStatus();
    if (status.running) {
      console.log('Augment is already running');
      return;
    }

    // Try common Augment executable locations
    const augmentPaths = [
      'C:\\Program Files\\Augment\\Augment.exe',
      '/Applications/Augment.app/Contents/MacOS/Augment',
      '/usr/local/bin/augment',
      'augment' // If in PATH
    ];

    for (const path of augmentPaths) {
      try {
        const child = spawn(path, ['--enable-mcp'], {
          detached: true,
          stdio: 'ignore'
        });
        
        child.unref();
        
        // Wait for Augment to start
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const newStatus = await this.checkAugmentStatus();
        if (newStatus.running) {
          this.emit('augment:started');
          return;
        }
      } catch (error) {
        // Try next path
      }
    }

    throw new Error('Could not start Augment. Please start it manually.');
  }

  /**
   * Disconnect
   */
  public disconnect(): void {
    if (this.client) {
      this.client.destroy();
      this.client = undefined;
      this.connected = false;
    }
  }
} 