"use strict";
/**
 * Test Your Specific Setup
 * Tests all configured tools based on your .env.pbw
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const ToolAuthenticator_1 = require("../src/pbw/ToolAuthenticator");
const AugmentMCPIntegration_1 = require("../src/pbw/adapters/AugmentMCPIntegration");
const PuppeteerAdapter_1 = require("../src/pbw/adapters/PuppeteerAdapter");
const ContextStorageAdapter_1 = require("../src/pbw/adapters/ContextStorageAdapter");
const dotenv = __importStar(require("dotenv"));
// Load your configuration
dotenv.config({ path: '.env.pbw' });
async function testYourSetup() {
    console.log('Testing your PBW setup...\n');
    const results = {};
    // 1. Test Claude API
    console.log('1. Testing Claude API:');
    try {
        const auth = new ToolAuthenticator_1.ToolAuthenticator();
        const result = await auth.authenticate('claude');
        results.claude = result.success;
        console.log(result.success ? '✓ Claude API authenticated' : '✗ Claude API failed');
    }
    catch (error) {
        results.claude = false;
        console.log('✗ Claude API error:', error.message);
    }
    // 2. Test Cursor (just check if enabled)
    console.log('\n2. Testing Cursor:');
    const cursorEnabled = process.env.PBW_CURSOR_ENABLED === 'true';
    results.cursor = cursorEnabled;
    console.log(cursorEnabled ? '✓ Cursor enabled (will connect when Cursor app is running)' : '✗ Cursor disabled');
    // 3. Test Augment MCP
    console.log('\n3. Testing Augment MCP:');
    if (process.env.PBW_AUGMENT_MCP_MODE === 'true') {
        try {
            const augment = new AugmentMCPIntegration_1.AugmentMCPIntegration();
            // Check if Augment is running
            const status = await augment.checkAugmentStatus();
            console.log(`  Augment running: ${status.running ? 'Yes' : 'No'}`);
            if (status.running) {
                await augment.connect();
                console.log('✓ Connected to Augment MCP');
                // Test a simple request
                const testCode = 'function add(a, b) { return a + b; }';
                const analysis = await augment.analyzeCode(testCode);
                console.log('✓ Augment code analysis working');
                augment.disconnect();
                results.augment = true;
            }
            else {
                console.log('⚠ Augment is not running. Start Augment to use MCP features.');
                results.augment = false;
            }
        }
        catch (error) {
            results.augment = false;
            console.log('✗ Augment MCP error:', error.message);
            console.log('  Make sure Augment is running with MCP enabled');
        }
    }
    else {
        results.augment = false;
        console.log('⊝ Augment MCP mode disabled');
    }
    // 4. Test Context Storage (our implementation)
    console.log('\n4. Testing Context Storage:');
    try {
        const context = new ContextStorageAdapter_1.ContextStorageAdapter();
        await context.initialize();
        await context.save('test-setup', { timestamp: new Date() });
        const value = await context.get('test-setup');
        results.context = value !== null;
        console.log('✓ Context storage working');
    }
    catch (error) {
        results.context = false;
        console.log('✗ Context storage error:', error.message);
    }
    // 5. Test Figma (just auth check)
    console.log('\n5. Testing Figma:');
    if (process.env.PBW_FIGMA_ENABLED === 'true') {
        const hasToken = process.env.PBW_FIGMA_ACCESS_TOKEN &&
            process.env.PBW_FIGMA_ACCESS_TOKEN !== 'your-figma-token-here';
        results.figma = hasToken;
        console.log(hasToken ? '✓ Figma token configured' : '⚠ Figma token not set');
    }
    else {
        results.figma = false;
        console.log('⊝ Figma disabled');
    }
    // 6. Test Puppeteer
    console.log('\n6. Testing Puppeteer:');
    if (process.env.PBW_PUPPETEER_ENABLED === 'true') {
        try {
            const puppeteer = new PuppeteerAdapter_1.PuppeteerAdapter({ headless: true });
            await puppeteer.initialize();
            const result = await puppeteer.executeTest('return 1 + 1');
            await puppeteer.close();
            results.puppeteer = result.success && result.result === 2;
            console.log('✓ Puppeteer working');
        }
        catch (error) {
            results.puppeteer = false;
            console.log('✗ Puppeteer error:', error.message);
        }
    }
    else {
        results.puppeteer = false;
        console.log('⊝ Puppeteer disabled');
    }
    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('SETUP SUMMARY:');
    console.log('='.repeat(50));
    const tools = Object.entries(results);
    const working = tools.filter(([_, status]) => status).length;
    tools.forEach(([tool, status]) => {
        console.log(`${status ? '✓' : '✗'} ${tool.charAt(0).toUpperCase() + tool.slice(1)}`);
    });
    console.log(`\nTotal: ${working}/${tools.length} tools ready`);
    if (working >= 3) {
        console.log('\n✅ Your setup is ready to use!');
        if (!results.augment) {
            console.log('\n💡 Tip: Start Augment to enable code analysis features');
        }
        if (!results.figma) {
            console.log('💡 Tip: Add your Figma token to enable design features');
        }
    }
    else {
        console.log('\n⚠️  You need at least 3 working tools to proceed effectively');
    }
}
// Run the test
testYourSetup().catch(console.error);
