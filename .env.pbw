﻿# PBW Tool Authentication Configuration
# Copy this file to .env.pbw and fill in your credentials

# Claude API
PBW_CLAUDE_API_KEY=************************************************************************************************************
PBW_CLAUDE_ENDPOINT=https://api.anthropic.com/v1
PBW_CLAUDE_ENABLED=true

# Claude Code (Disabled)
PBW_CLAUDE_CODE_ACCESS_TOKEN=disabled
PBW_CLAUDE_CODE_ENABLED=false

# Cursor Ultra (Local session)
PBW_CURSOR_ACCESS_TOKEN=local-session
PBW_CURSOR_ENDPOINT=http://localhost:3000/api
PBW_CURSOR_ENABLED=true

# Augment (Disabled until MCP server is set up)
PBW_AUGMENT_ENABLED=true
PBW_AUGMENT_MCP_MODE=true
PBW_AUGMENT_API_KEY=disabled
PBW_AUGMENT_API_SECRET=disabled

# Context7 (Our implementation)
PBW_CONTEXT7_ENABLED=true
PBW_CONTEXT7_USE_LOCAL=true

# Figma/Locofy (Standard REST API - This works!)
PBW_FIGMA_ENABLED=true
PBW_FIGMA_ACCESS_TOKEN=*********************************************
PBW_FIGMA_ENDPOINT=https://api.figma.com/v1

# Puppeteer (local - no auth needed)
PBW_PUPPETEER_ENABLED=true