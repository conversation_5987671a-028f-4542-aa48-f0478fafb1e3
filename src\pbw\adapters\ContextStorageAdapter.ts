/**
 * ContextStorageAdapter
 * Phase 01: PBW Foundation & Tool Setup
 * Simple context storage (replaces Context7 MCP)
 */

import { EventEmitter } from 'events';
import * as fs from 'fs/promises';
import * as path from 'path';

export interface ContextEntry {
  key: string;
  value: any;
  metadata: {
    created: Date;
    updated: Date;
    phase?: number;
    tool?: string;
    tags?: string[];
  };
}

export class ContextStorageAdapter extends EventEmitter {
  private contextPath: string;
  private cache: Map<string, ContextEntry> = new Map();
  private initialized: boolean = false;

  constructor(contextPath: string = './memory/context') {
    super();
    this.contextPath = contextPath;
  }

  /**
   * Initialize storage
   */
  public async initialize(): Promise<void> {
    try {
      await fs.mkdir(this.contextPath, { recursive: true });
      await this.loadContext();
      this.initialized = true;
      this.emit('initialized');
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Save context
   */
  public async save(key: string, value: any, metadata?: Partial<ContextEntry['metadata']>): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    const now = new Date();
    const existing = this.cache.get(key);

    const entry: ContextEntry = {
      key,
      value,
      metadata: {
        created: existing?.metadata.created || now,
        updated: now,
        ...metadata
      }
    };

    this.cache.set(key, entry);
    
    // Persist to disk
    const filePath = path.join(this.contextPath, `${key}.json`);
    await fs.writeFile(filePath, JSON.stringify(entry, null, 2));
    
    this.emit('saved', { key, size: JSON.stringify(value).length });
  }

  /**
   * Get context
   */
  public async get(key: string): Promise<any> {
    if (!this.initialized) {
      await this.initialize();
    }

    const entry = this.cache.get(key);
    if (entry) {
      return entry.value;
    }

    // Try to load from disk
    try {
      const filePath = path.join(this.contextPath, `${key}.json`);
      const content = await fs.readFile(filePath, 'utf-8');
      const loaded = JSON.parse(content) as ContextEntry;
      
      // Restore dates
      loaded.metadata.created = new Date(loaded.metadata.created);
      loaded.metadata.updated = new Date(loaded.metadata.updated);
      
      this.cache.set(key, loaded);
      return loaded.value;
    } catch (error) {
      return null;
    }
  }

  /**
   * Analyze context
   */
  public async analyze(pattern?: string): Promise<{
    totalEntries: number;
    totalSize: number;
    byPhase: Record<number, number>;
    byTool: Record<string, number>;
    recent: ContextEntry[];
  }> {
    if (!this.initialized) {
      await this.initialize();
    }

    const entries = Array.from(this.cache.values());
    const filtered = pattern 
      ? entries.filter(e => e.key.includes(pattern))
      : entries;

    const stats = {
      totalEntries: filtered.length,
      totalSize: 0,
      byPhase: {} as Record<number, number>,
      byTool: {} as Record<string, number>,
      recent: [] as ContextEntry[]
    };

    filtered.forEach(entry => {
      const size = JSON.stringify(entry.value).length;
      stats.totalSize += size;

      if (entry.metadata.phase) {
        stats.byPhase[entry.metadata.phase] = (stats.byPhase[entry.metadata.phase] || 0) + 1;
      }

      if (entry.metadata.tool) {
        stats.byTool[entry.metadata.tool] = (stats.byTool[entry.metadata.tool] || 0) + 1;
      }
    });

    stats.recent = filtered
      .sort((a, b) => b.metadata.updated.getTime() - a.metadata.updated.getTime())
      .slice(0, 10);

    return stats;
  }

  /**
   * Search context
   */
  public async search(query: {
    phase?: number;
    tool?: string;
    tags?: string[];
    after?: Date;
    before?: Date;
  }): Promise<ContextEntry[]> {
    if (!this.initialized) {
      await this.initialize();
    }

    return Array.from(this.cache.values()).filter(entry => {
      if (query.phase && entry.metadata.phase !== query.phase) return false;
      if (query.tool && entry.metadata.tool !== query.tool) return false;
      if (query.after && entry.metadata.updated < query.after) return false;
      if (query.before && entry.metadata.updated > query.before) return false;
      if (query.tags && query.tags.length > 0) {
        const entryTags = entry.metadata.tags || [];
        if (!query.tags.some(tag => entryTags.includes(tag))) return false;
      }
      return true;
    });
  }

  /**
   * Load all context from disk
   */
  private async loadContext(): Promise<void> {
    try {
      const files = await fs.readdir(this.contextPath);
      const jsonFiles = files.filter(f => f.endsWith('.json'));

      for (const file of jsonFiles) {
        try {
          const content = await fs.readFile(path.join(this.contextPath, file), 'utf-8');
          const entry = JSON.parse(content) as ContextEntry;
          
          // Restore dates
          entry.metadata.created = new Date(entry.metadata.created);
          entry.metadata.updated = new Date(entry.metadata.updated);
          
          this.cache.set(entry.key, entry);
        } catch (error) {
          console.warn(`Failed to load context file ${file}:`, error);
        }
      }

      this.emit('loaded', { count: this.cache.size });
    } catch (error) {
      // Directory might not exist yet
      if (error.code !== 'ENOENT') {
        throw error;
      }
    }
  }

  /**
   * Clear all context
   */
  public async clear(): Promise<void> {
    this.cache.clear();
    
    try {
      const files = await fs.readdir(this.contextPath);
      for (const file of files) {
        if (file.endsWith('.json')) {
          await fs.unlink(path.join(this.contextPath, file));
        }
      }
    } catch (error) {
      // Ignore if directory doesn't exist
    }
    
    this.emit('cleared');
  }
} 