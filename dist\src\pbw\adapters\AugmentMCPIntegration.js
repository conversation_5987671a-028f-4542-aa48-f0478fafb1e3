"use strict";
/**
 * AugmentMCPIntegration
 * Phase 01: PBW Foundation & Tool Setup
 * Integrates with Augment's built-in MCP server
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AugmentMCPIntegration = void 0;
const events_1 = require("events");
const child_process_1 = require("child_process");
const util_1 = require("util");
const net = __importStar(require("net"));
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class AugmentMCPIntegration extends events_1.EventEmitter {
    constructor() {
        super(...arguments);
        this.connected = false;
        this.messageQueue = new Map();
    }
    /**
     * Check if Augment is running
     */
    async checkAugmentStatus() {
        try {
            // Check for Augment process on Windows
            const { stdout } = await execAsync('tasklist /FI "IMAGENAME eq Augment.exe" /FO CSV');
            const isRunning = stdout.includes('Augment.exe');
            if (!isRunning) {
                // Check for Mac/Linux
                const { stdout: psOut } = await execAsync('ps aux | grep -i augment | grep -v grep');
                if (psOut.trim()) {
                    return { running: true };
                }
            }
            // Try to find MCP port from Augment config
            const mcpPort = await this.findAugmentMCPPort();
            return {
                running: isRunning || false,
                mcpPort
            };
        }
        catch (error) {
            return { running: false };
        }
    }
    /**
     * Find Augment's MCP port from config
     */
    async findAugmentMCPPort() {
        // Augment typically uses these locations for config
        const configPaths = [
            `${process.env.APPDATA}/Augment/config.json`,
            `${process.env.HOME}/.augment/config.json`,
            `${process.env.HOME}/Library/Application Support/Augment/config.json`
        ];
        for (const path of configPaths) {
            try {
                const fs = await Promise.resolve().then(() => __importStar(require('fs/promises')));
                const config = JSON.parse(await fs.readFile(path, 'utf-8'));
                if (config.mcp?.port) {
                    return config.mcp.port;
                }
            }
            catch (error) {
                // Continue to next path
            }
        }
        // Default Augment MCP port
        return 50051;
    }
    /**
     * Connect to Augment's MCP server
     */
    async connect() {
        const status = await this.checkAugmentStatus();
        if (!status.running) {
            throw new Error('Augment is not running. Please start Augment first.');
        }
        const port = status.mcpPort || 50051;
        return new Promise((resolve, reject) => {
            this.client = new net.Socket();
            this.client.connect(port, 'localhost', () => {
                this.connected = true;
                this.emit('connected', { port });
                resolve();
            });
            this.client.on('data', (data) => {
                this.handleData(data);
            });
            this.client.on('error', (error) => {
                this.connected = false;
                if (error.code === 'ECONNREFUSED') {
                    reject(new Error(`Cannot connect to Augment MCP on port ${port}. Make sure Augment is running with MCP enabled.`));
                }
                else {
                    reject(error);
                }
            });
            this.client.on('close', () => {
                this.connected = false;
                this.emit('disconnected');
            });
            // Timeout after 5 seconds
            setTimeout(() => {
                if (!this.connected) {
                    reject(new Error('Connection timeout. Augment MCP might not be enabled.'));
                }
            }, 5000);
        });
    }
    /**
     * Send MCP request to Augment
     */
    async request(method, params) {
        if (!this.connected || !this.client) {
            throw new Error('Not connected to Augment MCP');
        }
        const id = `${Date.now()}-${Math.random()}`;
        const message = {
            jsonrpc: '2.0',
            id,
            method,
            params: params || {}
        };
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                this.messageQueue.delete(id);
                reject(new Error(`Request timeout: ${method}`));
            }, 30000);
            this.messageQueue.set(id, (response) => {
                clearTimeout(timeout);
                this.messageQueue.delete(id);
                if (response.error) {
                    reject(new Error(response.error.message || 'Unknown error'));
                }
                else {
                    resolve(response.result);
                }
            });
            this.client.write(JSON.stringify(message) + '\n');
        });
    }
    /**
     * Handle incoming data
     */
    handleData(data) {
        const lines = data.toString().split('\n').filter(line => line.trim());
        lines.forEach(line => {
            try {
                const message = JSON.parse(line);
                if (message.id && this.messageQueue.has(message.id)) {
                    const callback = this.messageQueue.get(message.id);
                    callback(message);
                }
                else if (message.method) {
                    // Server notification
                    this.emit('notification', message);
                }
            }
            catch (error) {
                this.emit('parse:error', { error, data: line });
            }
        });
    }
    /**
     * Augment-specific MCP methods
     */
    /**
     * Get code suggestions
     */
    async getSuggestions(context) {
        return this.request('textDocument/completion', {
            textDocument: { uri: `file://${context.file}` },
            position: context.position,
            context: { triggerKind: 1 }
        });
    }
    /**
     * Analyze code
     */
    async analyzeCode(code, language = 'typescript') {
        return this.request('augment/analyze', {
            code,
            language,
            options: {
                includeStyleIssues: true,
                includeSecurity: true,
                includePerformance: true
            }
        });
    }
    /**
     * Generate tests
     */
    async generateTests(code, framework = 'jest') {
        return this.request('augment/generateTests', {
            code,
            framework,
            options: {
                coverage: 'high',
                includeEdgeCases: true
            }
        });
    }
    /**
     * Refactor code
     */
    async refactorCode(code, refactorType) {
        return this.request('augment/refactor', {
            code,
            refactorType,
            options: {
                preserveComments: true,
                modernize: true
            }
        });
    }
    /**
     * Start Augment if not running
     */
    async startAugment() {
        const status = await this.checkAugmentStatus();
        if (status.running) {
            console.log('Augment is already running');
            return;
        }
        // Try common Augment executable locations
        const augmentPaths = [
            'C:\\Program Files\\Augment\\Augment.exe',
            '/Applications/Augment.app/Contents/MacOS/Augment',
            '/usr/local/bin/augment',
            'augment' // If in PATH
        ];
        for (const path of augmentPaths) {
            try {
                const child = (0, child_process_1.spawn)(path, ['--enable-mcp'], {
                    detached: true,
                    stdio: 'ignore'
                });
                child.unref();
                // Wait for Augment to start
                await new Promise(resolve => setTimeout(resolve, 3000));
                const newStatus = await this.checkAugmentStatus();
                if (newStatus.running) {
                    this.emit('augment:started');
                    return;
                }
            }
            catch (error) {
                // Try next path
            }
        }
        throw new Error('Could not start Augment. Please start it manually.');
    }
    /**
     * Disconnect
     */
    disconnect() {
        if (this.client) {
            this.client.destroy();
            this.client = undefined;
            this.connected = false;
        }
    }
}
exports.AugmentMCPIntegration = AugmentMCPIntegration;
