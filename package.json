{"name": "patch-ai", "version": "1.0.0", "main": "index.js", "directories": {"doc": "docs", "test": "tests"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^24.1.0", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.17.0", "crypto": "^1.0.1", "dotenv": "^17.2.1", "events": "^3.3.0", "fs": "^0.0.1-security", "path": "^0.12.7", "puppeteer": "^24.15.0"}}