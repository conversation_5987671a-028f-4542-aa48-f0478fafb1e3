"use strict";
/**
 * PuppeteerAdapter
 * Phase 01: PBW Foundation & Tool Setup
 * Direct Puppeteer integration (no MCP needed)
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PuppeteerAdapter = void 0;
const events_1 = require("events");
const puppeteer_1 = __importDefault(require("puppeteer"));
class PuppeteerAdapter extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.config = {
            headless: true,
            defaultTimeout: 30000,
            viewportWidth: 1920,
            viewportHeight: 1080,
            ...config
        };
    }
    /**
     * Initialize browser
     */
    async initialize() {
        try {
            this.browser = await puppeteer_1.default.launch({
                headless: this.config.headless,
                args: ['--no-sandbox', '--disable-setuid-sandbox']
            });
            this.emit('initialized');
        }
        catch (error) {
            this.emit('error', error);
            throw error;
        }
    }
    /**
     * Execute browser test
     */
    async executeTest(script) {
        if (!this.browser) {
            await this.initialize();
        }
        const page = await this.browser.newPage();
        try {
            await page.setViewport({
                width: this.config.viewportWidth,
                height: this.config.viewportHeight
            });
            // Execute the test script
            const result = await page.evaluate(script);
            return {
                success: true,
                result,
                metrics: await page.metrics()
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
        finally {
            await page.close();
        }
    }
    /**
     * Test a URL
     */
    async testURL(url, tests) {
        if (!this.browser) {
            await this.initialize();
        }
        const page = await this.browser.newPage();
        try {
            await page.goto(url, { waitUntil: 'networkidle2' });
            const results = [];
            for (const test of tests) {
                try {
                    switch (test.action) {
                        case 'click':
                            await page.click(test.selector);
                            results.push({ selector: test.selector, action: 'click', success: true });
                            break;
                        case 'type':
                            await page.type(test.selector, test.value || '');
                            results.push({ selector: test.selector, action: 'type', success: true });
                            break;
                        case 'exists':
                        default:
                            const exists = await page.$(test.selector) !== null;
                            results.push({ selector: test.selector, exists, success: true });
                            break;
                    }
                }
                catch (error) {
                    results.push({
                        selector: test.selector,
                        action: test.action,
                        success: false,
                        error: error.message
                    });
                }
            }
            return {
                url,
                success: true,
                results,
                screenshot: await page.screenshot({ encoding: 'base64' })
            };
        }
        catch (error) {
            return {
                url,
                success: false,
                error: error.message
            };
        }
        finally {
            await page.close();
        }
    }
    /**
     * Capture screenshot
     */
    async screenshot(url, fullPage = false) {
        if (!this.browser) {
            await this.initialize();
        }
        const page = await this.browser.newPage();
        try {
            await page.goto(url, { waitUntil: 'networkidle2' });
            const screenshot = await page.screenshot({ fullPage });
            return Buffer.from(screenshot);
        }
        finally {
            await page.close();
        }
    }
    /**
     * Close browser
     */
    async close() {
        if (this.browser) {
            await this.browser.close();
            this.browser = undefined;
        }
    }
}
exports.PuppeteerAdapter = PuppeteerAdapter;
