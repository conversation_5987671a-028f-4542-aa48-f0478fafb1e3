"use strict";
/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BluetoothEventSchema = exports.BluetoothCommandSchema = exports.Bluetooth = void 0;
/**
 * THIS FILE IS AUTOGENERATED by cddlconv 0.1.6.
 * Run `node tools/generate-bidi-types.mjs` to regenerate.
 * @see https://github.com/w3c/webdriver-bidi/blob/master/index.bs
 */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck Some types may be circular.
const zod_1 = __importDefault(require("zod"));
var Bluetooth;
(function (Bluetooth) {
    Bluetooth.BluetoothUuidSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.BluetoothManufacturerDataSchema = zod_1.default.lazy(() => zod_1.default.object({
        key: zod_1.default.number().int().nonnegative(),
        data: zod_1.default.string(),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.CharacteristicPropertiesSchema = zod_1.default.lazy(() => zod_1.default.object({
        broadcast: zod_1.default.boolean().optional(),
        read: zod_1.default.boolean().optional(),
        writeWithoutResponse: zod_1.default.boolean().optional(),
        write: zod_1.default.boolean().optional(),
        notify: zod_1.default.boolean().optional(),
        indicate: zod_1.default.boolean().optional(),
        authenticatedSignedWrites: zod_1.default.boolean().optional(),
        extendedProperties: zod_1.default.boolean().optional(),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.RequestDeviceSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.RequestDeviceInfoSchema = zod_1.default.lazy(() => zod_1.default.object({
        id: Bluetooth.RequestDeviceSchema,
        name: zod_1.default.union([zod_1.default.string(), zod_1.default.null()]),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.RequestDevicePromptSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.ScanRecordSchema = zod_1.default.lazy(() => zod_1.default.object({
        name: zod_1.default.string().optional(),
        uuids: zod_1.default.array(Bluetooth.BluetoothUuidSchema).optional(),
        appearance: zod_1.default.number().optional(),
        manufacturerData: zod_1.default
            .array(Bluetooth.BluetoothManufacturerDataSchema)
            .optional(),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
exports.BluetoothCommandSchema = zod_1.default.lazy(() => zod_1.default.union([
    Bluetooth.HandleRequestDevicePromptSchema,
    Bluetooth.SimulateAdapterSchema,
    Bluetooth.DisableSimulationSchema,
    Bluetooth.SimulatePreconnectedPeripheralSchema,
    Bluetooth.SimulateAdvertisementSchema,
    Bluetooth.SimulateGattConnectionResponseSchema,
    Bluetooth.SimulateGattDisconnectionSchema,
    Bluetooth.SimulateServiceSchema,
    Bluetooth.SimulateCharacteristicSchema,
    Bluetooth.SimulateCharacteristicResponseSchema,
    Bluetooth.SimulateDescriptorSchema,
    Bluetooth.SimulateDescriptorResponseSchema,
    zod_1.default.object({}),
]));
(function (Bluetooth) {
    Bluetooth.HandleRequestDevicePromptSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.handleRequestDevicePrompt'),
        params: Bluetooth.HandleRequestDevicePromptParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.HandleRequestDevicePromptParametersSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        context: zod_1.default.string(),
        prompt: Bluetooth.RequestDevicePromptSchema,
    })
        .and(zod_1.default.union([
        Bluetooth.HandleRequestDevicePromptAcceptParametersSchema,
        Bluetooth.HandleRequestDevicePromptCancelParametersSchema,
    ])));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.HandleRequestDevicePromptAcceptParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        accept: zod_1.default.literal(true),
        device: Bluetooth.RequestDeviceSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.HandleRequestDevicePromptCancelParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        accept: zod_1.default.literal(false),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateAdapterSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.simulateAdapter'),
        params: Bluetooth.SimulateAdapterParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateAdapterParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        leSupported: zod_1.default.boolean().optional(),
        state: zod_1.default.enum(['absent', 'powered-off', 'powered-on']),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.DisableSimulationSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.disableSimulation'),
        params: Bluetooth.DisableSimulationParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.DisableSimulationParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulatePreconnectedPeripheralSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.simulatePreconnectedPeripheral'),
        params: Bluetooth.SimulatePreconnectedPeripheralParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulatePreconnectedPeripheralParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        address: zod_1.default.string(),
        name: zod_1.default.string(),
        manufacturerData: zod_1.default.array(Bluetooth.BluetoothManufacturerDataSchema),
        knownServiceUuids: zod_1.default.array(Bluetooth.BluetoothUuidSchema),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateAdvertisementSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.simulateAdvertisement'),
        params: Bluetooth.SimulateAdvertisementParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateAdvertisementParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        scanEntry: Bluetooth.SimulateAdvertisementScanEntryParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateAdvertisementScanEntryParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        deviceAddress: zod_1.default.string(),
        rssi: zod_1.default.number(),
        scanRecord: Bluetooth.ScanRecordSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateGattConnectionResponseSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.simulateGattConnectionResponse'),
        params: Bluetooth.SimulateGattConnectionResponseParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateGattConnectionResponseParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        address: zod_1.default.string(),
        code: zod_1.default.number().int().nonnegative(),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateGattDisconnectionSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.simulateGattDisconnection'),
        params: Bluetooth.SimulateGattDisconnectionParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateGattDisconnectionParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        address: zod_1.default.string(),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateServiceSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.simulateService'),
        params: Bluetooth.SimulateServiceParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateServiceParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        address: zod_1.default.string(),
        uuid: Bluetooth.BluetoothUuidSchema,
        type: zod_1.default.enum(['add', 'remove']),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateCharacteristicSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.simulateCharacteristic'),
        params: Bluetooth.SimulateCharacteristicParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateCharacteristicParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        address: zod_1.default.string(),
        serviceUuid: Bluetooth.BluetoothUuidSchema,
        characteristicUuid: Bluetooth.BluetoothUuidSchema,
        characteristicProperties: Bluetooth.CharacteristicPropertiesSchema.optional(),
        type: zod_1.default.enum(['add', 'remove']),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateCharacteristicResponseSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.simulateCharacteristicResponse'),
        params: Bluetooth.SimulateCharacteristicResponseParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateCharacteristicResponseParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        address: zod_1.default.string(),
        serviceUuid: Bluetooth.BluetoothUuidSchema,
        characteristicUuid: Bluetooth.BluetoothUuidSchema,
        type: zod_1.default.enum([
            'read',
            'write',
            'subscribe-to-notifications',
            'unsubscribe-from-notifications',
        ]),
        code: zod_1.default.number().int().nonnegative(),
        data: zod_1.default.array(zod_1.default.number().int().nonnegative()).optional(),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateDescriptorSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.simulateDescriptor'),
        params: Bluetooth.SimulateDescriptorParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateDescriptorParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        address: zod_1.default.string(),
        serviceUuid: Bluetooth.BluetoothUuidSchema,
        characteristicUuid: Bluetooth.BluetoothUuidSchema,
        descriptorUuid: Bluetooth.BluetoothUuidSchema,
        type: zod_1.default.enum(['add', 'remove']),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateDescriptorResponseSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.simulateDescriptorResponse'),
        params: Bluetooth.SimulateDescriptorResponseParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateDescriptorResponseParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        address: zod_1.default.string(),
        serviceUuid: Bluetooth.BluetoothUuidSchema,
        characteristicUuid: Bluetooth.BluetoothUuidSchema,
        descriptorUuid: Bluetooth.BluetoothUuidSchema,
        type: zod_1.default.enum(['read', 'write']),
        code: zod_1.default.number().int().nonnegative(),
        data: zod_1.default.array(zod_1.default.number().int().nonnegative()).optional(),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
exports.BluetoothEventSchema = zod_1.default.lazy(() => zod_1.default.union([
    Bluetooth.RequestDevicePromptUpdatedSchema,
    Bluetooth.GattConnectionAttemptedSchema,
]));
(function (Bluetooth) {
    Bluetooth.RequestDevicePromptUpdatedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.requestDevicePromptUpdated'),
        params: Bluetooth.RequestDevicePromptUpdatedParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.RequestDevicePromptUpdatedParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        prompt: Bluetooth.RequestDevicePromptSchema,
        devices: zod_1.default.array(Bluetooth.RequestDeviceInfoSchema),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.GattConnectionAttemptedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.gattConnectionAttempted'),
        params: Bluetooth.GattConnectionAttemptedParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.GattConnectionAttemptedParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        address: zod_1.default.string(),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.CharacteristicEventGeneratedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.characteristicEventGenerated'),
        params: Bluetooth.CharacteristicEventGeneratedParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.CharacteristicEventGeneratedParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        address: zod_1.default.string(),
        serviceUuid: Bluetooth.BluetoothUuidSchema,
        characteristicUuid: Bluetooth.BluetoothUuidSchema,
        type: zod_1.default.enum([
            'read',
            'write-with-response',
            'write-without-response',
            'subscribe-to-notifications',
            'unsubscribe-from-notifications',
        ]),
        data: zod_1.default.array(zod_1.default.number().int().nonnegative()).optional(),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.DescriptorEventGeneratedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('bluetooth.descriptorEventGenerated'),
        params: Bluetooth.DescriptorEventGeneratedParametersSchema,
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.DescriptorEventGeneratedParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        address: zod_1.default.string(),
        serviceUuid: Bluetooth.BluetoothUuidSchema,
        characteristicUuid: Bluetooth.BluetoothUuidSchema,
        descriptorUuid: Bluetooth.BluetoothUuidSchema,
        type: zod_1.default.enum(['read', 'write']),
        data: zod_1.default.array(zod_1.default.number().int().nonnegative()).optional(),
    }));
})(Bluetooth || (exports.Bluetooth = Bluetooth = {}));
//# sourceMappingURL=webdriver-bidi-bluetooth.js.map