"use strict";
/**
 * ToolAuthenticator
 * Phase 01: PBW Foundation & Tool Setup
 * Handles authentication for all 7 tools
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolAuthenticator = void 0;
const events_1 = require("events");
const crypto = __importStar(require("crypto"));
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
class ToolAuthenticator extends events_1.EventEmitter {
    constructor() {
        super();
        this.authConfigs = new Map();
        this.activeTokens = new Map();
        this.authStrategies = new Map();
        this.configPath = './.env.pbw';
        this.tokenCachePath = './memory/auth-tokens.json';
        this.initializeStrategies();
    }
    /**
     * Initialize authentication strategies for each tool
     */
    initializeStrategies() {
        // Claude API - API Key authentication
        this.authStrategies.set('claude', new ApiKeyAuthStrategy());
        // Claude Code - OAuth2 flow
        this.authStrategies.set('claude-code', new OAuth2AuthStrategy());
        // Cursor Ultra - Token-based auth
        this.authStrategies.set('cursor', new TokenAuthStrategy());
        // Augment - API Key + Secret
        this.authStrategies.set('augment', new ApiSecretAuthStrategy());
        // Context7 - Custom token auth
        this.authStrategies.set('context7', new CustomTokenAuthStrategy());
        // Figma - OAuth2 with refresh
        this.authStrategies.set('figma', new OAuth2RefreshAuthStrategy());
        // Puppeteer - No auth needed (local)
        this.authStrategies.set('puppeteer', new NoAuthStrategy());
    }
    /**
     * Load authentication configuration from environment
     */
    async loadConfig() {
        try {
            // Check if config file exists
            const configExists = await fs.access(this.configPath).then(() => true).catch(() => false);
            if (!configExists) {
                throw new Error('Authentication config file not found. Please create .env.pbw');
            }
            // Read config file
            const configContent = await fs.readFile(this.configPath, 'utf-8');
            const lines = configContent.split('\n');
            // Parse configuration
            lines.forEach(line => {
                const trimmed = line.trim();
                if (!trimmed || trimmed.startsWith('#'))
                    return;
                const [key, value] = trimmed.split('=').map(s => s.trim());
                if (!key || !value)
                    return;
                // Map environment variables to auth configs
                const [prefix, toolId, configKey] = key.split('_');
                if (prefix !== 'PBW')
                    return;
                const tool = toolId.toLowerCase().replace('_', '-');
                const config = this.authConfigs.get(tool) || { toolId: tool };
                switch (configKey) {
                    case 'API_KEY':
                        config.apiKey = value;
                        break;
                    case 'API_SECRET':
                        config.apiSecret = value;
                        break;
                    case 'ACCESS_TOKEN':
                        config.accessToken = value;
                        break;
                    case 'REFRESH_TOKEN':
                        config.refreshToken = value;
                        break;
                    case 'ENDPOINT':
                        config.endpoint = value;
                        break;
                    default:
                        if (configKey.startsWith('HEADER_')) {
                            config.customHeaders = config.customHeaders || {};
                            config.customHeaders[configKey.replace('HEADER_', '')] = value;
                        }
                }
                this.authConfigs.set(tool, config);
            });
            // Load cached tokens
            await this.loadTokenCache();
        }
        catch (error) {
            this.emit('config:error', error);
            throw error;
        }
    }
    /**
     * Load cached authentication tokens
     */
    async loadTokenCache() {
        try {
            const cacheExists = await fs.access(this.tokenCachePath).then(() => true).catch(() => false);
            if (!cacheExists)
                return;
            const cacheContent = await fs.readFile(this.tokenCachePath, 'utf-8');
            const tokens = JSON.parse(cacheContent);
            Object.entries(tokens).forEach(([toolId, tokenData]) => {
                if (new Date(tokenData.expiresAt) > new Date()) {
                    this.activeTokens.set(toolId, {
                        ...tokenData,
                        expiresAt: new Date(tokenData.expiresAt)
                    });
                }
            });
        }
        catch (error) {
            console.warn('Failed to load token cache:', error);
        }
    }
    /**
     * Save authentication tokens to cache
     */
    async saveTokenCache() {
        try {
            const tokens = {};
            this.activeTokens.forEach((token, toolId) => {
                tokens[toolId] = {
                    ...token,
                    expiresAt: token.expiresAt.toISOString()
                };
            });
            await fs.mkdir(path.dirname(this.tokenCachePath), { recursive: true });
            await fs.writeFile(this.tokenCachePath, JSON.stringify(tokens, null, 2));
        }
        catch (error) {
            console.warn('Failed to save token cache:', error);
        }
    }
    /**
     * Authenticate a specific tool
     */
    async authenticate(toolId) {
        this.emit('auth:start', { toolId });
        try {
            // Load configuration if not already loaded
            if (this.authConfigs.size === 0) {
                await this.loadConfig();
            }
            const config = this.authConfigs.get(toolId);
            if (!config) {
                throw new Error(`No authentication configuration found for tool: ${toolId}`);
            }
            const strategy = this.authStrategies.get(toolId);
            if (!strategy) {
                throw new Error(`No authentication strategy found for tool: ${toolId}`);
            }
            // Check if we have a valid cached token
            const cachedToken = this.activeTokens.get(toolId);
            if (cachedToken && cachedToken.expiresAt > new Date()) {
                this.emit('auth:cached', { toolId });
                return {
                    toolId,
                    success: true,
                    expiresAt: cachedToken.expiresAt,
                    metadata: { fromCache: true }
                };
            }
            // Perform authentication
            const result = await strategy.authenticate(config);
            if (result.success && result.token) {
                // Cache the token
                this.activeTokens.set(toolId, result.token);
                await this.saveTokenCache();
            }
            this.emit('auth:complete', { toolId, success: result.success });
            return {
                toolId,
                success: result.success,
                expiresAt: result.token?.expiresAt,
                error: result.error
            };
        }
        catch (error) {
            const authError = error;
            this.emit('auth:error', { toolId, error: authError });
            return {
                toolId,
                success: false,
                error: authError
            };
        }
    }
    /**
     * Authenticate all configured tools
     */
    async authenticateAll() {
        await this.loadConfig();
        const tools = ['claude', 'claude-code', 'cursor', 'augment', 'context7', 'figma', 'puppeteer'];
        const results = await Promise.all(tools.map(toolId => this.authenticate(toolId)));
        const summary = {
            total: results.length,
            successful: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length
        };
        this.emit('auth:summary', summary);
        return results;
    }
    /**
     * Verify authentication status for a tool
     */
    async verify(toolId) {
        const token = this.activeTokens.get(toolId);
        if (!token)
            return false;
        // Check expiration
        if (token.expiresAt <= new Date()) {
            // Try to refresh if possible
            if (token.refreshToken) {
                const result = await this.refresh(toolId);
                return result.success;
            }
            return false;
        }
        // Verify with the service
        const strategy = this.authStrategies.get(toolId);
        if (strategy && strategy.verify) {
            return await strategy.verify(token);
        }
        return true;
    }
    /**
     * Refresh authentication for a tool
     */
    async refresh(toolId) {
        const token = this.activeTokens.get(toolId);
        const config = this.authConfigs.get(toolId);
        const strategy = this.authStrategies.get(toolId);
        if (!token || !config || !strategy) {
            return {
                toolId,
                success: false,
                error: new Error('Missing authentication components for refresh')
            };
        }
        if (!strategy.refresh) {
            // Re-authenticate if refresh not supported
            return await this.authenticate(toolId);
        }
        try {
            const result = await strategy.refresh(config, token);
            if (result.success && result.token) {
                this.activeTokens.set(toolId, result.token);
                await this.saveTokenCache();
            }
            return {
                toolId,
                success: result.success,
                expiresAt: result.token?.expiresAt,
                error: result.error
            };
        }
        catch (error) {
            return {
                toolId,
                success: false,
                error: error
            };
        }
    }
    /**
     * Get authentication headers for a tool
     */
    getHeaders(toolId) {
        const token = this.activeTokens.get(toolId);
        const config = this.authConfigs.get(toolId);
        const strategy = this.authStrategies.get(toolId);
        if (!token || !strategy)
            return {};
        const headers = strategy.getHeaders(token);
        // Add custom headers if configured
        if (config?.customHeaders) {
            Object.assign(headers, config.customHeaders);
        }
        return headers;
    }
    /**
     * Revoke authentication for a tool
     */
    async revoke(toolId) {
        this.activeTokens.delete(toolId);
        await this.saveTokenCache();
        this.emit('auth:revoked', { toolId });
    }
}
exports.ToolAuthenticator = ToolAuthenticator;
/**
 * Base authentication strategy
 */
class AuthStrategy {
}
/**
 * API Key authentication strategy
 */
class ApiKeyAuthStrategy extends AuthStrategy {
    async authenticate(config) {
        if (!config.apiKey) {
            return { success: false, error: new Error('API key not provided') };
        }
        return {
            success: true,
            token: {
                toolId: config.toolId,
                token: config.apiKey,
                expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
            }
        };
    }
    getHeaders(token) {
        return {
            'Authorization': `Bearer ${token.token}`,
            'X-API-Key': token.token
        };
    }
}
/**
 * OAuth2 authentication strategy
 */
class OAuth2AuthStrategy extends AuthStrategy {
    async authenticate(config) {
        // Simplified OAuth2 - in real implementation would handle full flow
        if (!config.accessToken) {
            return { success: false, error: new Error('Access token not provided') };
        }
        return {
            success: true,
            token: {
                toolId: config.toolId,
                token: config.accessToken,
                refreshToken: config.refreshToken,
                expiresAt: new Date(Date.now() + 60 * 60 * 1000) // 1 hour
            }
        };
    }
    async refresh(config, token) {
        if (!token.refreshToken) {
            return { success: false, error: new Error('No refresh token available') };
        }
        // Simulate refresh - real implementation would call OAuth endpoint
        return {
            success: true,
            token: {
                ...token,
                token: crypto.randomBytes(32).toString('hex'),
                expiresAt: new Date(Date.now() + 60 * 60 * 1000)
            }
        };
    }
    getHeaders(token) {
        return {
            'Authorization': `Bearer ${token.token}`
        };
    }
}
/**
 * Token-based authentication strategy
 */
class TokenAuthStrategy extends AuthStrategy {
    async authenticate(config) {
        if (!config.accessToken) {
            return { success: false, error: new Error('Access token not provided') };
        }
        return {
            success: true,
            token: {
                toolId: config.toolId,
                token: config.accessToken,
                expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
            }
        };
    }
    getHeaders(token) {
        return {
            'X-Auth-Token': token.token
        };
    }
}
/**
 * API Key + Secret authentication strategy
 */
class ApiSecretAuthStrategy extends AuthStrategy {
    async authenticate(config) {
        if (!config.apiKey || !config.apiSecret) {
            return { success: false, error: new Error('API key or secret not provided') };
        }
        // Generate signature
        const timestamp = Date.now().toString();
        const signature = crypto
            .createHmac('sha256', config.apiSecret)
            .update(`${config.apiKey}:${timestamp}`)
            .digest('hex');
        return {
            success: true,
            token: {
                toolId: config.toolId,
                token: signature,
                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
                scope: ['full_access']
            }
        };
    }
    getHeaders(token) {
        return {
            'X-API-Key': this.getApiKey(token),
            'X-API-Signature': token.token,
            'X-Timestamp': Date.now().toString()
        };
    }
    getApiKey(token) {
        // In real implementation, would store/retrieve the API key securely
        return 'stored-api-key';
    }
}
/**
 * Custom token authentication strategy
 */
class CustomTokenAuthStrategy extends AuthStrategy {
    async authenticate(config) {
        if (!config.accessToken) {
            return { success: false, error: new Error('Custom token not provided') };
        }
        return {
            success: true,
            token: {
                toolId: config.toolId,
                token: config.accessToken,
                expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
            }
        };
    }
    async verify(token) {
        // Custom verification logic
        return token.expiresAt > new Date();
    }
    getHeaders(token) {
        return {
            'Context7-Token': token.token,
            'Context7-Version': '1.0'
        };
    }
}
/**
 * OAuth2 with refresh authentication strategy
 */
class OAuth2RefreshAuthStrategy extends OAuth2AuthStrategy {
    async authenticate(config) {
        const result = await super.authenticate(config);
        if (result.success && result.token) {
            // Set shorter expiration to test refresh
            result.token.expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
        }
        return result;
    }
}
/**
 * No authentication strategy (for local tools)
 */
class NoAuthStrategy extends AuthStrategy {
    async authenticate(config) {
        return {
            success: true,
            token: {
                toolId: config.toolId,
                token: 'local',
                expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
            }
        };
    }
    getHeaders(token) {
        return {};
    }
}
