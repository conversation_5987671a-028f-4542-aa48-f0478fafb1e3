"use strict";
/**
 * ContextStorageAdapter
 * Phase 01: PBW Foundation & Tool Setup
 * Simple context storage (replaces Context7 MCP)
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextStorageAdapter = void 0;
const events_1 = require("events");
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
class ContextStorageAdapter extends events_1.EventEmitter {
    constructor(contextPath = './memory/context') {
        super();
        this.cache = new Map();
        this.initialized = false;
        this.contextPath = contextPath;
    }
    /**
     * Initialize storage
     */
    async initialize() {
        try {
            await fs.mkdir(this.contextPath, { recursive: true });
            await this.loadContext();
            this.initialized = true;
            this.emit('initialized');
        }
        catch (error) {
            this.emit('error', error);
            throw error;
        }
    }
    /**
     * Save context
     */
    async save(key, value, metadata) {
        if (!this.initialized) {
            await this.initialize();
        }
        const now = new Date();
        const existing = this.cache.get(key);
        const entry = {
            key,
            value,
            metadata: {
                created: existing?.metadata.created || now,
                updated: now,
                ...metadata
            }
        };
        this.cache.set(key, entry);
        // Persist to disk
        const filePath = path.join(this.contextPath, `${key}.json`);
        await fs.writeFile(filePath, JSON.stringify(entry, null, 2));
        this.emit('saved', { key, size: JSON.stringify(value).length });
    }
    /**
     * Get context
     */
    async get(key) {
        if (!this.initialized) {
            await this.initialize();
        }
        const entry = this.cache.get(key);
        if (entry) {
            return entry.value;
        }
        // Try to load from disk
        try {
            const filePath = path.join(this.contextPath, `${key}.json`);
            const content = await fs.readFile(filePath, 'utf-8');
            const loaded = JSON.parse(content);
            // Restore dates
            loaded.metadata.created = new Date(loaded.metadata.created);
            loaded.metadata.updated = new Date(loaded.metadata.updated);
            this.cache.set(key, loaded);
            return loaded.value;
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Analyze context
     */
    async analyze(pattern) {
        if (!this.initialized) {
            await this.initialize();
        }
        const entries = Array.from(this.cache.values());
        const filtered = pattern
            ? entries.filter(e => e.key.includes(pattern))
            : entries;
        const stats = {
            totalEntries: filtered.length,
            totalSize: 0,
            byPhase: {},
            byTool: {},
            recent: []
        };
        filtered.forEach(entry => {
            const size = JSON.stringify(entry.value).length;
            stats.totalSize += size;
            if (entry.metadata.phase) {
                stats.byPhase[entry.metadata.phase] = (stats.byPhase[entry.metadata.phase] || 0) + 1;
            }
            if (entry.metadata.tool) {
                stats.byTool[entry.metadata.tool] = (stats.byTool[entry.metadata.tool] || 0) + 1;
            }
        });
        stats.recent = filtered
            .sort((a, b) => b.metadata.updated.getTime() - a.metadata.updated.getTime())
            .slice(0, 10);
        return stats;
    }
    /**
     * Search context
     */
    async search(query) {
        if (!this.initialized) {
            await this.initialize();
        }
        return Array.from(this.cache.values()).filter(entry => {
            if (query.phase && entry.metadata.phase !== query.phase)
                return false;
            if (query.tool && entry.metadata.tool !== query.tool)
                return false;
            if (query.after && entry.metadata.updated < query.after)
                return false;
            if (query.before && entry.metadata.updated > query.before)
                return false;
            if (query.tags && query.tags.length > 0) {
                const entryTags = entry.metadata.tags || [];
                if (!query.tags.some(tag => entryTags.includes(tag)))
                    return false;
            }
            return true;
        });
    }
    /**
     * Load all context from disk
     */
    async loadContext() {
        try {
            const files = await fs.readdir(this.contextPath);
            const jsonFiles = files.filter(f => f.endsWith('.json'));
            for (const file of jsonFiles) {
                try {
                    const content = await fs.readFile(path.join(this.contextPath, file), 'utf-8');
                    const entry = JSON.parse(content);
                    // Restore dates
                    entry.metadata.created = new Date(entry.metadata.created);
                    entry.metadata.updated = new Date(entry.metadata.updated);
                    this.cache.set(entry.key, entry);
                }
                catch (error) {
                    console.warn(`Failed to load context file ${file}:`, error);
                }
            }
            this.emit('loaded', { count: this.cache.size });
        }
        catch (error) {
            // Directory might not exist yet
            if (error.code !== 'ENOENT') {
                throw error;
            }
        }
    }
    /**
     * Clear all context
     */
    async clear() {
        this.cache.clear();
        try {
            const files = await fs.readdir(this.contextPath);
            for (const file of files) {
                if (file.endsWith('.json')) {
                    await fs.unlink(path.join(this.contextPath, file));
                }
            }
        }
        catch (error) {
            // Ignore if directory doesn't exist
        }
        this.emit('cleared');
    }
}
exports.ContextStorageAdapter = ContextStorageAdapter;
